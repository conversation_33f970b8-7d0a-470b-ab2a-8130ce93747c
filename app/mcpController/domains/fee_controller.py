#!/usr/bin/env python3
"""
费用域控制器 - 应用充值相关功能
提供应用数量充值等功能
"""
import logging

from fastapi import APIRouter, Query

from mcpService.domains.fee_service import recharge_app_quota

# 配置日志
logger = logging.getLogger(__name__)

# 创建费用域路由器
fee_router = APIRouter(
    prefix="/fee",
    tags=["费用域"],
    responses={404: {"description": "Not found"}}
)


@fee_router.post(
    "/recharge_app_quota",
    summary="💰 充值应用数量",
    description="为指定企业充值数量配额，支持模拟环境和测试环境",
    operation_id="fee_recharge_app_quota"
)
async def recharge_app_quota_endpoint(
        env: str = Query(..., description="环境类型，如'模拟环境'"),
        name: str = Query(..., description="企业名称，默认为空，与gid同时存在取gid"),
        gid: str = Query(..., description="gid，默认为空，与name同时存在取gid")
):
    """
    充值应用数量
    
    参数说明:
    - env: 环境类型，通常为"模拟环境"
    - name: 企业名称，用于标识要充值的企业
    - gid: gid，用于标识要充值的企业

    返回示例:
    {
      "status": "success",
      "message": "充值应用数量成功",
      "data": {
        "env": "模拟环境",
        "name": "esigntest李保辉头发有限公司",
        "gid": "111"
      }
    }
    """
    logger.info(f"充值应用数量请求: env={env}, name={name}, gid={gid}")

    result = recharge_app_quota(
        env=env,
        name=name,
        gid=gid
    )
    return result


# 导出路由器
router = fee_router
