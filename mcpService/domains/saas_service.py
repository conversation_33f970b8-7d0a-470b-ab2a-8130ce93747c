#!/usr/bin/env python3
"""
SaaS域服务 - SaaS平台相关功能实现
提供账号管理、组织管理等功能
"""
import logging
from datetime import time
from typing import Dict, Any, Optional
from mcpService.common.base_service import BaseService

logger = logging.getLogger(__name__)


class SaasService(BaseService):
    """SaaS服务类"""
    
    def __init__(self):
        super().__init__(domain="saas")


# 全局SaaS服务实例
saas_service = SaasService()


def register_test_person_account(
    app_id: str,
    idNo: str,
    mobile: str,
    name: str,
    thirdPartyUserId: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    注册测试个人账号
    
    Args:
        app_id: 应用ID
        idNo: 身份证号
        mobile: 手机号
        name: 姓名
        thirdPartyUserId: 第三方用户ID
        environment: 环境描述，支持自然语言
        
    Returns:
        注册结果
    """
    try:
        # 构建请求数据
        request_data = {
            "idNo": idNo,
            "mobile": mobile,
            "name": name,
            "thirdPartyUserId": thirdPartyUserId
        }
        
        # 添加应用ID到请求头
        headers = {"X-Tsign-Open-App-Id": app_id}
        
        result = saas_service.make_api_request(
            path="/v1/accounts/createByThirdPartyUserId",
            data=request_data,
            headers=headers,
            environment=environment,
            service="footstone",
            operation="注册个人账号"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"注册个人账号异常: {str(e)}")
        return saas_service.formatter.error(
            message=f"注册个人账号异常: {str(e)}",
            details={"name": name, "mobile": mobile}
        )


def register_test_company_account(
    app_id: str,
    idNumber: str,
    mobile: str,
    name: str,
    thirdPartyUserId: str,
    orgLegalIdNumber: str,
    orgLegalName: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    注册测试企业账号
    
    Args:
        app_id: 应用ID
        idNumber: 身份证号
        mobile: 手机号
        name: 姓名
        thirdPartyUserId: 第三方用户ID
        orgLegalIdNumber: 企业法人身份证号
        orgLegalName: 企业法人姓名
        environment: 环境描述，支持自然语言
        
    Returns:
        注册结果
    """
    try:
        # 构建请求数据
        request_data = {
            "idNumber": idNumber,
            "mobile": mobile,
            "name": name,
            "thirdPartyUserId": thirdPartyUserId,
            "orgLegalIdNumber": orgLegalIdNumber,
            "orgLegalName": orgLegalName
        }
        
        # 添加应用ID到请求头
        headers = {"X-Tsign-Open-App-Id": app_id}
        
        result = saas_service.make_api_request(
            path="/v1/organizations/createByThirdPartyUserId",
            data=request_data,
            headers=headers,
            environment=environment,
            service="footstone",
            operation="注册企业账号"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"注册企业账号异常: {str(e)}")
        return saas_service.formatter.error(
            message=f"注册企业账号异常: {str(e)}",
            details={"name": name, "orgLegalName": orgLegalName}
        )


def create_organization(
    org_name: str,
    org_code: str,
    legal_name: str,
    legal_idcard: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建组织
    
    Args:
        org_name: 组织名称
        org_code: 组织代码
        legal_name: 法人姓名
        legal_idcard: 法人身份证号
        environment: 环境描述，支持自然语言
        
    Returns:
        创建结果
    """
    try:
        # 构建请求数据
        request_data = {
            "orgName": org_name,
            "orgCode": org_code,
            "legalName": legal_name,
            "legalIdcard": legal_idcard
        }
        
        result = saas_service.make_api_request(
            path="/organization/create",
            data=request_data,
            environment=environment,
            operation="创建组织"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"创建组织异常: {str(e)}")
        return saas_service.formatter.error(
            message=f"创建组织异常: {str(e)}",
            details={"org_name": org_name}
        )


def add_member_to_org(
    org_id: str,
    member_name: str,
    member_mobile: str,
    member_idcard: str,
    role: str = "member",
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    添加组织成员
    
    Args:
        org_id: 组织ID
        member_name: 成员姓名
        member_mobile: 成员手机号
        member_idcard: 成员身份证号
        role: 角色
        environment: 环境描述，支持自然语言
        
    Returns:
        添加结果
    """
    try:
        # 构建请求数据
        request_data = {
            "orgId": org_id,
            "memberName": member_name,
            "memberMobile": member_mobile,
            "memberIdcard": member_idcard,
            "role": role
        }
        
        result = saas_service.make_api_request(
            path="/organization/addMember",
            data=request_data,
            environment=environment,
            operation="添加组织成员"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"添加组织成员异常: {str(e)}")
        return saas_service.formatter.error(
            message=f"添加组织成员异常: {str(e)}",
            details={"org_id": org_id, "member_name": member_name}
        )


def query_org_info(
    org_id: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    查询组织信息
    
    Args:
        org_id: 组织ID
        environment: 环境描述，支持自然语言
        
    Returns:
        组织信息
    """
    try:
        # 构建请求数据
        request_data = {
            "orgId": org_id
        }
        
        result = saas_service.make_api_request(
            path="/organization/query",
            data=request_data,
            environment=environment,
            operation="查询组织信息"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"查询组织信息异常: {str(e)}")
        return saas_service.formatter.error(
            message=f"查询组织信息异常: {str(e)}",
            details={"org_id": org_id}
        )


def batch_open_vip(
    gid_list: list,
    vip_code: str,
    expire_date_start_str: int ,
    expire_date_end_str: int ,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    批量开通VIP
    
    Args:
        gid_list: GID列表
        vip_code: VIP代码
        expire_date_start_str: VIP有效期开始时间戳
        expire_date_end_str: VIP有效期结束时间戳
        vip_level: VIP等级
        environment: 环境描述，支持自然语言
        
    Returns:
        开通结果
    """
    try:
        # 构建请求数据
        request_data = {
            "gidList": gid_list,
            "vipCode": vip_code,
            "expireDateStartStr": expire_date_start_str,
            "expireDateEndStr": expire_date_end_str
        }
        
        headers = {
            "x-timevale-jwtcontent": "eyJhbGlhcyI6Iuemu-atjCIsImlkIjoibGlnZSJ9"
        }
        
        result = saas_service.make_api_request(
            path="/v2/saas-common/vipmanage/users/batchOpenVip",
            data=request_data,
            headers=headers,
            environment=environment,
            service="saas-common-manage",
            operation="批量开通VIP"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"批量开通VIP异常: {str(e)}")
        return saas_service.formatter.error(
            message=f"批量开通VIP异常: {str(e)}",
            details={
                "gid_list": gid_list, 
                "vip_code": vip_code
            }
        )
