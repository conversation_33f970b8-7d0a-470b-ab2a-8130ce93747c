#!/usr/bin/env python3
"""
费用域服务 - 企业充值相关功能实现
基于新的架构重构，使用统一的基础服务类
"""
import logging
from typing import Dict, Any

from mcpService.common.base_service import BaseService

logger = logging.getLogger(__name__)


class FeeService(BaseService):
    """费用服务类"""

    def __init__(self):
        super().__init__(domain="fee")


# 全局费用服务实例
fee_service = FeeService()


def recharge_app_quota(
        env: str,
        name=None,
        gid=None
) -> Dict[str, Any]:
    """
    充值应用数量
    
    Args:
        env: 环境类型，如"测试环境，模拟环境"
        name: 企业名称，默认值为空
        gid: gid，默认值为空
    Returns:
        充值结果
    """
    try:
        request_data = {"env": env}
        # 如果gid里没有数字
        if not gid or not gid.isdigit():
            request_data["name"] = name
        else:
            request_data["gid"] = gid
        headers = {'operator': 'mcp'}
        result = fee_service.make_api_request(
            url="http://sdk.testk8s.tsign.cn/fee/app",
            data=request_data,
            method="POST",
            service="fee",
            operation="充值应用数量",
            environment=env,
            headers=headers
        )
        logger.info(f"充值应用数量入参: {request_data}")
        logger.info(f"充值应用数量结果: {result}")
        return result

    except Exception as e:
        logger.error(f"充值应用数量异常: {str(e)}")
        return fee_service.formatter.error(
            message=f"充值应用数量异常: {str(e)}",
            details={"env": env, "name": name, "gid": gid}
        )
